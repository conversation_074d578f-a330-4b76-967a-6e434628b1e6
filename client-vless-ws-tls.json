{"log": {"level": "info", "timestamp": true}, "dns": {"servers": [{"tag": "google", "address": "*******", "strategy": "prefer_ipv4"}, {"tag": "local", "address": "*********", "strategy": "prefer_ipv4", "detour": "direct"}], "rules": [{"rule_set": "geosite-cn", "server": "local"}], "final": "google", "strategy": "prefer_ipv4"}, "inbounds": [{"type": "mixed", "tag": "mixed-in", "listen": "127.0.0.1", "listen_port": 7890, "sniff": true, "sniff_override_destination": true}, {"type": "tun", "tag": "tun-in", "interface_name": "tun0", "inet4_address": "**********/30", "mtu": 9000, "auto_route": true, "strict_route": true, "sniff": true, "sniff_override_destination": true}], "outbounds": [{"type": "vless", "tag": "vless-ws-tls-out", "server": "your-domain.com", "server_port": 443, "uuid": "550e8400-e29b-41d4-a716-************", "flow": "", "network": "tcp", "tls": {"enabled": true, "server_name": "your-domain.com", "insecure": false, "alpn": ["h2", "http/1.1"]}, "transport": {"type": "ws", "path": "/vless-ws-tls", "headers": {"Host": "your-domain.com"}, "max_early_data": 2048, "early_data_header_name": "Sec-WebSocket-Protocol"}}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"rules": [{"protocol": "dns", "outbound": "dns-out"}, {"ip_is_private": true, "outbound": "direct"}, {"rule_set": "geosite-cn", "outbound": "direct"}, {"rule_set": "geoip-cn", "outbound": "direct"}], "rule_set": [{"tag": "geosite-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-cn.srs"}, {"tag": "geoip-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geoip/rule-set/geoip-cn.srs"}], "final": "vless-ws-tls-out", "auto_detect_interface": true}}