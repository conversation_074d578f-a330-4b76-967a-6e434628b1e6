{"log": {"level": "info", "timestamp": true}, "inbounds": [{"type": "vless", "tag": "vless-ws-tls-in", "listen": "::", "listen_port": 443, "users": [{"name": "user1", "uuid": "550e8400-e29b-41d4-a716-************", "flow": ""}], "tls": {"enabled": true, "server_name": "example.com", "certificate_path": "/path/to/certificate.crt", "key_path": "/path/to/private.key", "alpn": ["h2", "http/1.1"]}, "transport": {"type": "ws", "path": "/vless-ws-tls", "headers": {"Host": "example.com"}, "max_early_data": 2048, "early_data_header_name": "Sec-WebSocket-Protocol"}}], "outbounds": [{"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}]}