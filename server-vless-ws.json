{"log": {"level": "info", "timestamp": true}, "inbounds": [{"type": "vless", "tag": "vless-ws-in", "listen": "::", "listen_port": 8080, "users": [{"name": "user1", "uuid": "550e8400-e29b-41d4-a716-************", "flow": ""}], "transport": {"type": "ws", "path": "/vless-ws", "headers": {"Host": "example.com"}, "max_early_data": 2048, "early_data_header_name": "Sec-WebSocket-Protocol"}}], "outbounds": [{"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}]}