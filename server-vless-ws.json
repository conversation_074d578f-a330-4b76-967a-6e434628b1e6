{"log": {"level": "info", "timestamp": true}, "inbounds": [{"type": "vless", "tag": "vless-ws-in", "listen": "::", "listen_port": 8080, "users": [{"uuid": "550e8400-e29b-41d4-a716-************", "flow": ""}], "transport": {"type": "ws", "path": "/vless-ws", "headers": {"Host": "example.com"}, "max_early_data": 2048, "early_data_header_name": "Sec-WebSocket-Protocol"}}], "outbounds": [{"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}], "route": {"rules": [{"protocol": "dns", "outbound": "dns-out"}, {"ip_is_private": true, "outbound": "direct"}, {"rule_set": "geosite-cn", "outbound": "direct"}, {"rule_set": "geoip-cn", "outbound": "direct"}], "rule_set": [{"tag": "geosite-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-cn.srs"}, {"tag": "geoip-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geoip/rule-set/geoip-cn.srs"}], "final": "direct", "auto_detect_interface": true}}