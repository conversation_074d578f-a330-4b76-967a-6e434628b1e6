# sing-box VLESS+WebSocket 快速配置指南

## 🚀 快速开始

### 1. 生成UUID
```bash
# Linux/macOS
uuidgen

# 或使用在线生成器
# https://www.uuidgenerator.net/
```

### 2. 服务器配置

#### VLESS+WebSocket (无TLS)
```json
{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "inbounds": [
    {
      "type": "vless",
      "tag": "vless-ws-in",
      "listen": "::",
      "listen_port": 8080,
      "users": [
        {
          "name": "user1",
          "uuid": "YOUR-UUID-HERE",
          "flow": ""
        }
      ],
      "transport": {
        "type": "ws",
        "path": "/vless-ws",
        "headers": {
          "Host": "your-domain.com"
        },
        "max_early_data": 2048,
        "early_data_header_name": "Sec-WebSocket-Protocol"
      }
    }
  ],
  "outbounds": [
    {
      "type": "direct",
      "tag": "direct"
    }
  ]
}
```

#### VLESS+WebSocket+TLS
```json
{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "inbounds": [
    {
      "type": "vless",
      "tag": "vless-ws-tls-in",
      "listen": "::",
      "listen_port": 443,
      "users": [
        {
          "name": "user1",
          "uuid": "YOUR-UUID-HERE",
          "flow": ""
        }
      ],
      "tls": {
        "enabled": true,
        "server_name": "your-domain.com",
        "certificate_path": "/path/to/certificate.crt",
        "key_path": "/path/to/private.key",
        "alpn": ["h2", "http/1.1"]
      },
      "transport": {
        "type": "ws",
        "path": "/vless-ws-tls",
        "headers": {
          "Host": "your-domain.com"
        },
        "max_early_data": 2048,
        "early_data_header_name": "Sec-WebSocket-Protocol"
      }
    }
  ],
  "outbounds": [
    {
      "type": "direct",
      "tag": "direct"
    }
  ]
}
```

### 3. 客户端配置

#### VLESS+WebSocket (无TLS)
```json
{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "inbounds": [
    {
      "type": "mixed",
      "tag": "mixed-in",
      "listen": "127.0.0.1",
      "listen_port": 7890,
      "sniff": true,
      "sniff_override_destination": true
    }
  ],
  "outbounds": [
    {
      "type": "vless",
      "tag": "vless-ws-out",
      "server": "YOUR-SERVER-IP",
      "server_port": 8080,
      "uuid": "YOUR-UUID-HERE",
      "flow": "",
      "network": "tcp",
      "transport": {
        "type": "ws",
        "path": "/vless-ws",
        "headers": {
          "Host": "your-domain.com"
        },
        "max_early_data": 2048,
        "early_data_header_name": "Sec-WebSocket-Protocol"
      }
    },
    {
      "type": "direct",
      "tag": "direct"
    }
  ]
}
```

#### VLESS+WebSocket+TLS
```json
{
  "log": {
    "level": "info",
    "timestamp": true
  },
  "inbounds": [
    {
      "type": "mixed",
      "tag": "mixed-in",
      "listen": "127.0.0.1",
      "listen_port": 7890,
      "sniff": true,
      "sniff_override_destination": true
    }
  ],
  "outbounds": [
    {
      "type": "vless",
      "tag": "vless-ws-tls-out",
      "server": "your-domain.com",
      "server_port": 443,
      "uuid": "YOUR-UUID-HERE",
      "flow": "",
      "network": "tcp",
      "tls": {
        "enabled": true,
        "server_name": "your-domain.com",
        "insecure": false,
        "alpn": ["h2", "http/1.1"]
      },
      "transport": {
        "type": "ws",
        "path": "/vless-ws-tls",
        "headers": {
          "Host": "your-domain.com"
        },
        "max_early_data": 2048,
        "early_data_header_name": "Sec-WebSocket-Protocol"
      }
    },
    {
      "type": "direct",
      "tag": "direct"
    }
  ]
}
```

## 📝 配置步骤

1. **替换UUID**: 将 `YOUR-UUID-HERE` 替换为生成的UUID
2. **替换域名**: 将 `your-domain.com` 替换为您的实际域名
3. **替换服务器地址**: 将 `YOUR-SERVER-IP` 替换为服务器IP地址
4. **配置TLS证书**: 更新证书和私钥路径
5. **启动服务**: 使用 `./sing-box run -c config.json` 启动

## ⚠️ 注意事项

- 服务器和客户端的UUID必须完全一致
- 路径 (`path`) 和Host必须匹配
- TLS配置需要有效的SSL证书
- 确保防火墙开放相应端口

## 🔧 测试连接

```bash
# 测试HTTP代理
curl -x http://127.0.0.1:7890 http://www.google.com

# 测试SOCKS5代理
curl --socks5 127.0.0.1:7890 http://www.google.com
```
