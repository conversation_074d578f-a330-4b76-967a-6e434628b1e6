{"log": {"level": "info", "timestamp": true}, "inbounds": [{"type": "vless", "tag": "vless-ws-tls-in", "listen": "::", "listen_port": 443, "users": [{"uuid": "550e8400-e29b-41d4-a716-************", "flow": ""}], "tls": {"enabled": true, "server_name": "example.com", "certificate_path": "/path/to/certificate.crt", "key_path": "/path/to/private.key", "alpn": ["h2", "http/1.1"], "min_version": "1.2", "max_version": "1.3", "cipher_suites": ["TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384", "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384", "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305", "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305", "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256", "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"]}, "transport": {"type": "ws", "path": "/vless-ws-tls", "headers": {"Host": "example.com"}, "max_early_data": 2048, "early_data_header_name": "Sec-WebSocket-Protocol"}}], "outbounds": [{"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}], "route": {"rules": [{"protocol": "dns", "outbound": "dns-out"}, {"ip_is_private": true, "outbound": "direct"}, {"rule_set": "geosite-cn", "outbound": "direct"}, {"rule_set": "geoip-cn", "outbound": "direct"}], "rule_set": [{"tag": "geosite-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-cn.srs"}, {"tag": "geoip-cn", "type": "remote", "format": "binary", "url": "https://raw.githubusercontent.com/SagerNet/sing-geoip/rule-set/geoip-cn.srs"}], "final": "direct", "auto_detect_interface": true}}