# sing-box VLESS+WebSocket 配置说明

本文档提供了详细的 sing-box VLESS+WebSocket 和 VLESS+WebSocket+TLS 的服务器和客户端配置。

## 配置文件说明

### 1. 服务器配置

#### VLESS+WebSocket 服务器 (`server-vless-ws.json`)
- **监听端口**: 8080
- **协议**: VLESS over WebSocket
- **路径**: `/vless-ws`
- **UUID**: `550e8400-e29b-41d4-a716-************` (需要修改)

#### VLESS+WebSocket+TLS 服务器 (`server-vless-ws-tls.json`)
- **监听端口**: 443 (HTTPS标准端口)
- **协议**: VLESS over WebSocket with TLS
- **路径**: `/vless-ws-tls`
- **TLS**: 启用，支持 HTTP/2 和 HTTP/1.1
- **UUID**: `550e8400-e29b-41d4-a716-************` (需要修改)

### 2. 客户端配置

#### VLESS+WebSocket 客户端 (`client-vless-ws.json`)
- **本地代理端口**: 7890 (HTTP/SOCKS5混合代理)
- **TUN模式**: 启用，接口名 `tun0`
- **DNS**: 国内使用阿里DNS，国外使用Google DNS
- **分流规则**: 中国大陆直连，其他走代理

#### VLESS+WebSocket+TLS 客户端 (`client-vless-ws-tls.json`)
- **本地代理端口**: 7890 (HTTP/SOCKS5混合代理)
- **TUN模式**: 启用，接口名 `tun0`
- **TLS验证**: 启用，验证服务器证书
- **DNS**: 国内使用阿里DNS，国外使用Google DNS

## 配置修改指南

### 必须修改的参数

1. **UUID**: 
   - 生成新的UUID替换 `550e8400-e29b-41d4-a716-************`
   - 可以使用在线UUID生成器或命令: `uuidgen` (Linux/macOS)

2. **服务器地址**:
   - 客户端配置中的 `"server"` 字段
   - VLESS+WS: 修改为您的服务器IP地址
   - VLESS+WS+TLS: 修改为您的域名

3. **域名和Host**:
   - 所有配置中的 `example.com` 或 `your-domain.com`
   - 修改为您的实际域名

4. **TLS证书路径** (仅TLS配置):
   - `"certificate_path"`: SSL证书文件路径
   - `"key_path"`: 私钥文件路径

### 可选修改的参数

1. **端口号**:
   - 服务器监听端口可以根据需要修改
   - 客户端本地代理端口 (默认7890)

2. **WebSocket路径**:
   - 可以修改为自定义路径，增加安全性
   - 服务器和客户端必须保持一致

3. **日志级别**:
   - 可选: `trace`, `debug`, `info`, `warn`, `error`
   - 生产环境建议使用 `warn` 或 `error`

## 启动命令

### 服务器端
```bash
# VLESS+WebSocket
./sing-box run -c server-vless-ws.json

# VLESS+WebSocket+TLS
./sing-box run -c server-vless-ws-tls.json
```

### 客户端
```bash
# VLESS+WebSocket
./sing-box run -c client-vless-ws.json

# VLESS+WebSocket+TLS
./sing-box run -c client-vless-ws-tls.json
```

## 注意事项

1. **防火墙设置**: 确保服务器防火墙开放相应端口
2. **TLS证书**: TLS配置需要有效的SSL证书
3. **域名解析**: TLS配置需要域名正确解析到服务器IP
4. **权限要求**: TUN模式可能需要管理员权限运行
5. **时间同步**: 确保服务器和客户端时间同步

## 故障排除

1. **连接失败**: 检查服务器地址、端口、UUID是否正确
2. **TLS握手失败**: 检查证书是否有效，域名是否匹配
3. **DNS解析问题**: 检查DNS服务器设置
4. **路由问题**: 检查分流规则配置

## 安全建议

1. 定期更换UUID
2. 使用复杂的WebSocket路径
3. 启用TLS加密
4. 定期更新sing-box版本
5. 监控服务器日志
